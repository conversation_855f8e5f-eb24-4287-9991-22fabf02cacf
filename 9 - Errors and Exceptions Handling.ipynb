def add(n1, n2):
    print(n1 + n2)

add(10, 20)

number1 = 10

number2 = input("Please provide a number: ")

add(number1, number2)

try:
    result = 10 + 10
except:
    print("Hey it looks like you aren't adding correctly!")
else:
    print("Add went well!")


try:
    result = 10 + '10'
except:
    print("Hey it looks like you aren't adding correctly!")
else:
    print("Add went well!")

try:
    f = open('testfile', 'w')
    f.write("Write a test line")
except TypeError:
    print("There was a type error!")
except OSError:
    print("Hey you have an OS Error")
finally:
    print("I always run")

try:
    f = open('testfile', 'r')
    f.write("Write a test line")
except TypeError:
    print("There was a type error!")
except OSError:
    print("Hey you have an OS Error")
finally:
    print("I always run")