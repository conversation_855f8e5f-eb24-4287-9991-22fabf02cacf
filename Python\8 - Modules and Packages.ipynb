%pip install requests -q
%pip install colorama -q

from colorama import Fore

print(Fore.RED + "Hello World")

print(Fore.GREEN + "Hello World")

#one.py

def func():
    print("FUNC() IN ONE.PY")

print("TOP LEVEL IN ONE.PY")

if __name__ == '__main__':
    print('ONE.PY is being run directly!')
else:
    print('ONE.PY has been imported!')


print("TOP LEVEL IN TWO.PY")
func()

if __name__ == '__main__':
    print('TWO.PY is being run directly!')
else:
    print('TWO.PY has been imported!')