{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["___\n", "\n", "<a href='https://www.udemy.com/user/joseportilla/'><img src='../<PERSON>ian_Data_Logo.png'/></a>\n", "___\n", "<center><em>Content Copyright by Pierian Data</em></center>\n", "# Objects and Data Structures Assessment Test"]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["## Test your knowledge. \n", "\n", "** Answer the following questions **"]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["Write (or just say out loud to yourself) a brief description of all the following Object Types and Data Structures we've learned about. You can edit the cell below by double clicking on it. Really this is just to test if you know the difference between these, so feel free to just think about it, since your answers are self-graded."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Double Click HERE to edit this markdown cell and write answers.\n", "\n", "Numbers: Stores numerical information\n", "\n", "Strings: Ordered Sequence of characters\n", "\n", "Lists: Ordered sequence of object(mutable)\n", "\n", "Tuples: Ordered Sequence of object(immutable)\n", "\n", "Dictionaries: Key-value pair that is unordered\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Numbers\n", "\n", "Write an equation that uses multiplication, division, an exponent, addition, and subtraction that is equal to 100.25.\n", "\n", "Hint: This is just to test your memory of the basic arithmetic commands, work backwards from 100.25"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["100.25"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["(8 * 5 + 2**3) - (20 / 4) + 57.25"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Answer these 3 questions without typing code. Then type code to check your answer.\n", "\n", "    What is the value of the expression 4 * (6 + 5)\n", "    \n", "    What is the value of the expression 4 * 6 + 5 \n", "    \n", "    What is the value of the expression 4 + 6 * 5 "]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["44"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["4 * (6 + 5)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["29"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["4 * 6 + 5"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["34"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["4 + 6 * 5"]}, {"cell_type": "markdown", "metadata": {}, "source": ["What is the *type* of the result of the expression 3 + 1.5 + 4?<br><br>\n", "\n", "Answer: Floating Point number"]}, {"cell_type": "markdown", "metadata": {}, "source": ["What would you use to find a number’s square root, as well as its square? "]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["10.0"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Square root:\n", "100 ** 0.5"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["100"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Square:\n", "10 ** 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Strings"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Given the string 'hello' give an index command that returns 'e'. Enter your code in the cell below:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["'e'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["s = 'hello'\n", "# Print out 'e' using indexing\n", "\n", "s[1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Reverse the string 'hello' using slicing:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["'olleh'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["s ='hello'\n", "# Reverse the string using slicing\n", "\n", "s[::-1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Given the string hello, give two methods of producing the letter 'o' using indexing."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["'o'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["s ='hello'\n", "# Print out the 'o'\n", "\n", "# Method 1:\n", "\n", "s[4]"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["'o'"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# Method 2:\n", "\n", "s[-1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Lists"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Build this list [0,0,0] two separate ways."]}, {"cell_type": "code", "execution_count": 13, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["[0, 0, 0]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# Method 1:\n", "list = [0,0,0]\n", "list"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["[0, 0, 0]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# Method 2:\n", "[0]*3"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Reassign 'hello' in this nested list to say 'goodbye' instead:"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["[1, 2, [3, 4, 'goodbye']]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["list3 = [1,2,[3,4,'hello']]\n", "\n", "list3[2][2] = 'goodbye'\n", "list3"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Sort the list below:"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["[1, 3, 4, 5, 6]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["list4 = [5,3,4,6,1]\n", "\n", "list4.sort()\n", "list4"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Dictionaries"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Using keys and indexing, grab the 'hello' from the following dictionaries:"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["'hello'"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["d = {'simple_key':'hello'}\n", "# Grab 'hello'\n", "d['simple_key']"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["'hello'"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["d = {'k1':{'k2':'hello'}}\n", "# Grab 'hello'\n", "d['k1']['k2']"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["'hello'"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# Getting a little tricker\n", "d = {'k1':[{'nest_key':['this is deep',['hello']]}]}\n", "\n", "#Grab hello\n", "d['k1'][0]['nest_key'][1][0]"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["'hello'"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# This will be hard and annoying!\n", "d = {'k1':[1,2,{'k2':['this is tricky',{'tough':[1,2,['hello']]}]}]}\n", "\n", "d['k1'][2]['k2'][1]['tough'][2][0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Can you sort a dictionary? Why or why not?<br><br>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Tu<PERSON>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["What is the major difference between tuples and lists?<br><br>\n", "\n", "**Tuples are immutable**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["How do you create a tuple?<br><br>"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["t = (1,2,3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Sets "]}, {"cell_type": "markdown", "metadata": {}, "source": ["What is unique about a set?<br><br>\n", "\n", "**They don't allow for duplicate items**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Use a set to find the unique values of the list below:"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["{1, 2, 3, 4, 11, 22, 33}"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["list5 = [1,2,2,33,4,4,11,22,3,3,2]\n", "\n", "unique_values = set(list5)\n", "unique_values"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Booleans"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For the following quiz questions, we will get a preview of comparison operators. In the table below, a=3 and b=4.\n", "\n", "<table class=\"table table-bordered\">\n", "<tr>\n", "<th style=\"width:10%\">Operator</th><th style=\"width:45%\">Description</th><th>Example</th>\n", "</tr>\n", "<tr>\n", "<td>==</td>\n", "<td>If the values of two operands are equal, then the condition becomes true.</td>\n", "<td> (a == b) is not true.</td>\n", "</tr>\n", "<tr>\n", "<td>!=</td>\n", "<td>If values of two operands are not equal, then condition becomes true.</td>\n", "<td> (a != b) is true.</td>\n", "</tr>\n", "<tr>\n", "<td>&gt;</td>\n", "<td>If the value of left operand is greater than the value of right operand, then condition becomes true.</td>\n", "<td> (a &gt; b) is not true.</td>\n", "</tr>\n", "<tr>\n", "<td>&lt;</td>\n", "<td>If the value of left operand is less than the value of right operand, then condition becomes true.</td>\n", "<td> (a &lt; b) is true.</td>\n", "</tr>\n", "<tr>\n", "<td>&gt;=</td>\n", "<td>If the value of left operand is greater than or equal to the value of right operand, then condition becomes true.</td>\n", "<td> (a &gt;= b) is not true. </td>\n", "</tr>\n", "<tr>\n", "<td>&lt;=</td>\n", "<td>If the value of left operand is less than or equal to the value of right operand, then condition becomes true.</td>\n", "<td> (a &lt;= b) is true. </td>\n", "</tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["What will be the resulting Boolean of the following pieces of code (answer fist then check by typing it in!)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["# Answer before running cell\n", "2 > 3\n", "# False"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["# Answer before running cell\n", "3 <= 2\n", "# False"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["# Answer before running cell\n", "3 == 2.0\n", "# False"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["# Answer before running cell\n", "3.0 == 3\n", "# True"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["# Answer before running cell\n", "4**0.5 != 2\n", "# False"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Final Question: What is the boolean output of the cell block below?"]}, {"cell_type": "code", "execution_count": 36, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["# two nested lists\n", "l_one = [1,2,[3,4]]\n", "l_two = [1,2,{'k1':4}]\n", "\n", "# True or False?\n", "l_one[2][0] >= l_two[2]['k1']\n", "# False"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Great Job on your first assessment! "]}], "metadata": {"anaconda-cloud": {}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 1}