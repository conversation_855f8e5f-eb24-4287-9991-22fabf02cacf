{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["___\n", "\n", "<a href='https://www.udemy.com/user/joseportilla/'><img src='../<PERSON>ian_Data_Logo.png'/></a>\n", "___\n", "<center><em>Content Copyright by Pierian Data</em></center>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Functions and Methods Homework \n", "\n", "Complete the following questions:\n", "____\n", "**Write a function that computes the volume of a sphere given its radius.**\n", "<p>The volume of a sphere is given as $$\\frac{4}{3} πr^3$$</p>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from math import pow,pi\n", "def vol(rad):\n", "    volume = (4/3) * pi * pow(rad,3)\n", "    print(volume)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["33.510321638291124\n"]}], "source": ["# Check\n", "vol(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["___\n", "**Write a function that checks whether a number is in a given range (inclusive of high and low)**"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def ran_check(num,low,high):\n", "    if low <= num <=high:\n", "        print(str(5)+\" is in the range between \"+str(low)+\" and \"+str(high))\n", "    pass"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5 is in the range between 2 and 7\n"]}], "source": ["# Check\n", "ran_check(5,2,7)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you only wanted to return a boolean:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def ran_bool(num,low,high):\n", "    if low<=num<=high:\n", "        print(\"True\")\n", "    else:\n", "        print(\"False\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["ran_bool(3,1,10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["____\n", "**Write a Python function that accepts a string and calculates the number of upper case letters and lower case letters.**\n", "\n", "    Sample String : 'Hello Mr<PERSON>, how are you this fine Tuesday?'\n", "    Expected Output : \n", "    No. of Upper case characters : 4\n", "    No. of Lower case Characters : 33\n", "\n", "HINT: Two string methods that might prove useful: **.isupper()** and **.islower()**\n", "\n", "If you feel ambitious, explore the Collections module to solve this problem!"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def up_low(s):\n", "    upper_count = 0\n", "    lower_count = 0\n", "    for i in s:\n", "        if i.isupper():\n", "            upper_count += 1\n", "        elif i.islower():\n", "            lower_count += 1\n", "            \n", "    print(\"No. of Upper case characters :\" + str(upper_count))\n", "    print(\"No. of Lower case characters :\" + str(lower_count))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No. of Upper case characters :4\n", "No. of Lower case characters :33\n"]}], "source": ["s = 'Hello Mr<PERSON>, how are you this fine Tuesday?'\n", "up_low(s)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["____\n", "**Write a Python function that takes a list and returns a new list with unique elements of the first list.**\n", "\n", "    Sample List : [1,1,1,1,2,2,3,3,3,3,4,5]\n", "    Unique List : [1, 2, 3, 4, 5]"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def unique_list(lst):\n", "    lst = list(set(lst))\n", "    print(lst)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 2, 3, 4, 5]\n"]}], "source": ["unique_list([1,1,1,1,2,2,3,3,3,3,4,5])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["____\n", "**Write a Python function to multiply all the numbers in a list.**\n", "\n", "    Sample List : [1, 2, 3, -4]\n", "    Expected Output : -24"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["def multiply(numbers):\n", "    multi = 1\n", "    for i in numbers:\n", "        multi *= i\n", "    print(multi)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-24\n"]}], "source": ["multiply([1,2,3,-4])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["____\n", "**Write a Python function that checks whether a word or phrase is palindrome or not.**\n", "\n", "Note: A palindrome is word, phrase, or sequence that reads the same backward as forward, e.g., madam,kayak,racecar, or a phrase \"nurses run\". Hint: You may want to check out the .replace() method in a string to help out with dealing with spaces. Also google search how to reverse a string in Python, there are some clever ways to do it with slicing notation."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["def palindrome(s):\n", "    if s == s[::-1]:\n", "        print(\"True\")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["palindrome('helleh')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["____\n", "#### Hard:\n", "\n", "**Write a Python function to check whether a string is pangram or not. (Assume the string passed in does not have any punctuation)**\n", "\n", "    Note : Pangrams are words or sentences containing every letter of the alphabet at least once.\n", "    For example : \"The quick brown fox jumps over the lazy dog\"\n", "\n", "Hint: You may want to use .replace() method to get rid of spaces.\n", "\n", "Hint: Look at the [string module](https://stackoverflow.com/questions/16060899/alphabet-range-in-python)\n", "\n", "Hint: In case you want to use [set comparisons](https://medium.com/better-programming/a-visual-guide-to-set-comparisons-in-python-6ab7edb9ec41)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["def ispangram(str1):\n", "    return set(string.ascii_lowercase) <= set(str1.lower())\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["ispangram(\"The quick brown fox jumps over the lazy dog\")"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["'abcdefghijklmnopqrstuvwxyz'"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["string.ascii_lowercase"]}, {"cell_type": "markdown", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}}, "source": ["#### Great Job!"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}